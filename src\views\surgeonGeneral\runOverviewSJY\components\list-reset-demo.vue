<template>
  <div class="demo-container">
    <h3>ContentList 数据变化重置演示</h3>
    
    <!-- 控制按钮 -->
    <div class="controls">
      <button @click="updateData">更新列表数据</button>
      <button @click="addMoreData">添加更多数据</button>
      <button @click="clearData">清空数据</button>
      <button @click="toggleAutoScroll">
        {{ autoScroll ? '关闭' : '开启' }}自动滚动
      </button>
    </div>
    
    <!-- 当前数据信息 -->
    <div class="info">
      <p>当前数据量: {{ testList.length }} 条</p>
      <p>自动滚动: {{ autoScroll ? '开启' : '关闭' }}</p>
      <p>更新次数: {{ updateCount }}</p>
    </div>
    
    <!-- ContentList 组件 -->
    <div class="list-container">
      <ContentList 
        :list="testList" 
        :option="{name: 'name', value: 'value', unit: '分'}"
        :auto-scroll="autoScroll"
        :scroll-speed="0.3"
        :scroll-delay="1000"
        @scroll-to-bottom="handleScrollToBottom"
      />
    </div>
  </div>
</template>

<script>
import ContentList from './contentList.vue'

export default {
  name: 'ListResetDemo',
  components: {
    ContentList
  },
  data() {
    return {
      autoScroll: true,
      updateCount: 0,
      testList: [
        { name: '项目A', value: 85 },
        { name: '项目B', value: 72 },
        { name: '项目C', value: 91 },
        { name: '项目D', value: 68 },
        { name: '项目E', value: 79 },
        { name: '项目F', value: 83 },
        { name: '项目G', value: 76 },
        { name: '项目H', value: 88 },
        { name: '项目I', value: 74 },
        { name: '项目J', value: 82 }
      ]
    }
  },
  methods: {
    // 更新列表数据
    updateData() {
      this.updateCount++
      this.testList = [
        { name: `新项目A-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目B-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目C-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目D-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目E-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目F-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目G-${this.updateCount}`, value: Math.floor(Math.random() * 100) },
        { name: `新项目H-${this.updateCount}`, value: Math.floor(Math.random() * 100) }
      ]
      console.log('数据已更新，列表应该重置到顶部并重新开始滚动')
    },
    
    // 添加更多数据
    addMoreData() {
      const moreData = []
      for (let i = 0; i < 5; i++) {
        moreData.push({
          name: `额外项目${i + 1}`,
          value: Math.floor(Math.random() * 100)
        })
      }
      this.testList = [...this.testList, ...moreData]
      console.log('添加了更多数据，列表应该重置到顶部')
    },
    
    // 清空数据
    clearData() {
      this.testList = []
      console.log('数据已清空，滚动应该停止')
    },
    
    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll
    },
    
    // 处理滚动到底部事件
    handleScrollToBottom() {
      console.log('列表滚动到底部了')
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.controls {
  margin-bottom: 20px;
  
  button {
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 8px 16px;
    background: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background: #66b1ff;
    }
  }
}

.info {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  
  p {
    margin: 5px 0;
    font-size: 14px;
  }
}

.list-container {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #2c3e50;
}
</style>
